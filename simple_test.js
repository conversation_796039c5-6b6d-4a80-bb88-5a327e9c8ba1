/**
 * 简单的Nike签到中心测试
 */
const wxcode = require('./wxcode');

const APPID = 'wx096c43d1829a7788';
const wxid = 'wxid_ltkystdcspc822';

async function testRedeemCenter() {
    console.log('🔔 开始Nike签到中心测试');

    // 启用详细日志
    process.env.LOGS = 1;

    try {
        // 1. 获取微信授权码
        console.log('🔑 获取微信授权码...');
        const codeResult = await wxcode.getWxCode(wxid, APPID);

        console.log('授权码API响应:', codeResult);

        if (!codeResult || !codeResult.success) {
            console.log(`❌ 获取授权码失败: ${codeResult?.error || '未知错误'}`);
            return;
        }

        console.log(`✅ 授权码获取成功: ${codeResult.code}`);
        
        // 2. 获取GC Token
        console.log('🔑 获取GC Token...');
        const gcLoginData = {
            appId: `wechat:mp:${APPID}`,
            code: codeResult.code
        };
        
        const gcResponse = await wxcode.httpRequest('POST', 
            'https://wechat.nike.com.cn/wechat_auth/token/v1', 
            gcLoginData, {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        );
        
        console.log('GC Token响应:', gcResponse);
        
        if (!gcResponse || !gcResponse.accessToken) {
            console.log('❌ 未获取到GC Token');
            return;
        }
        
        const gcToken = gcResponse.accessToken;
        console.log(`✅ GC Token获取成功: ${gcToken.substring(0, 50)}...`);
        
        // 3. 测试签到中心接口
        console.log('📊 测试签到中心接口...');
        const redeemResponse = await wxcode.httpRequest('GET',
            'https://wechat.nike.com.cn/onemp/redeem/redeem_center_info/v2',
            null, {
                'Authorization': `Bearer ${gcToken}`,
                'Accept': 'application/json'
            }
        );
        
        console.log('签到中心响应:', JSON.stringify(redeemResponse, null, 2));
        
        // 4. 解析任务列表
        if (redeemResponse && redeemResponse.code === 0 && redeemResponse.data) {
            console.log('✅ 签到中心接口调用成功');
            
            if (redeemResponse.data.tasks && Array.isArray(redeemResponse.data.tasks)) {
                console.log(`📋 找到 ${redeemResponse.data.tasks.length} 个任务:`);
                
                redeemResponse.data.tasks.forEach((task, index) => {
                    console.log(`  任务${index + 1}:`, {
                        id: task.id || task.taskId,
                        name: task.name,
                        category: task.category,
                        type: task.type,
                        status: task.status
                    });
                });
                
                // 查找签到任务
                const signTask = redeemResponse.data.tasks.find(task =>
                    task.category === 'SIGN' ||
                    task.type === 'DAILY' ||
                    task.name?.includes('签到') ||
                    task.name?.includes('sign')
                );
                
                if (signTask) {
                    const taskId = signTask.id || signTask.taskId;
                    console.log(`🎯 找到签到任务ID: ${taskId}`);
                    console.log(`📝 签到任务详情:`, signTask);
                    
                    // 5. 尝试执行签到
                    console.log(`✍️ 尝试执行签到，任务ID: ${taskId}`);
                    const signResponse = await wxcode.httpRequest('GET',
                        `https://wechat.nike.com.cn/onemp/redeem/complete_daily_sign/v2/${taskId}`,
                        null, {
                            'Authorization': `Bearer ${gcToken}`,
                            'Accept': 'application/json'
                        }
                    );
                    
                    console.log('签到响应:', JSON.stringify(signResponse, null, 2));
                } else {
                    console.log('⚠️ 未找到签到任务');
                }
            } else {
                console.log('⚠️ 响应中没有tasks数组');
            }
        } else {
            console.log('❌ 签到中心接口调用失败');
        }
        
    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
        console.error(error);
    }
}

// 运行测试
testRedeemCenter().then(() => {
    console.log('🎉 测试完成');
}).catch(error => {
    console.error('❌ 测试异常:', error);
});
